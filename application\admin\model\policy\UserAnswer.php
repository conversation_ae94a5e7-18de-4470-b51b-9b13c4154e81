<?php

namespace app\admin\model\policy;

use think\Model;

class UserAnswer extends Model
{
    // 表名
    protected $name = 'policy_user_answer';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;

    // 追加属性
    protected $append = [
        'answer_option_ids_array'
    ];

    /**
     * 获取答案选项ID数组
     */
    public function getAnswerOptionIdsArrayAttr($value, $data)
    {
        $ids = $value ? $value : (isset($data['answer_option_ids']) ? $data['answer_option_ids'] : '');
        return $ids ? explode(',', $ids) : [];
    }

    /**
     * 设置答案选项ID
     */
    public function setAnswerOptionIdsAttr($value)
    {
        return is_array($value) ? implode(',', $value) : $value;
    }

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo('app\common\model\User', 'user_id');
    }

    /**
     * 关联问卷
     */
    public function questionnaire()
    {
        return $this->belongsTo('Questionnaire', 'questionnaire_id');
    }

    /**
     * 关联问题
     */
    public function question()
    {
        return $this->belongsTo('Question', 'question_id');
    }

    /**
     * 关联答案选项（多对多关系，因为支持多选）
     */
    public function answerOptions()
    {
        return $this->belongsToMany('AnswerOption', 'policy_user_answer', 'answer_option_id', 'id', 'answer_option_ids');
    }

    /**
     * 保存用户答题记录
     */
    public static function saveUserAnswer($userId, $questionnaireId, $questionId, $answerOptionIds)
    {
        // 检查是否已经回答过该问题
        $existAnswer = self::where([
            'user_id' => $userId,
            'questionnaire_id' => $questionnaireId,
            'question_id' => $questionId
        ])->find();

        $data = [
            'user_id' => $userId,
            'questionnaire_id' => $questionnaireId,
            'question_id' => $questionId,
            'answer_option_ids' => is_array($answerOptionIds) ? implode(',', $answerOptionIds) : $answerOptionIds,
            'createtime' => time()
        ];

        if ($existAnswer) {
            // 更新已有答案
            return $existAnswer->save($data);
        } else {
            // 创建新答案
            $model = new self();
            return $model->save($data);
        }
    }

    /**
     * 获取用户在问卷中的所有答案
     */
    public static function getUserAnswers($userId, $questionnaireId)
    {
        return self::where([
            'user_id' => $userId,
            'questionnaire_id' => $questionnaireId
        ])->select();
    }

    /**
     * 检查用户是否完成了问卷
     */
    public static function isQuestionnaireCompleted($userId, $questionnaireId)
    {
        // 获取问卷的所有必答问题数量
        $requiredQuestionCount = Question::where([
            'questionnaire_id' => $questionnaireId,
            'is_required' => 1,
            'status' => 'normal'
        ])->count();

        // 获取用户已回答的必答问题数量
        $answeredRequiredCount = self::alias('ua')
            ->join('policy_question q', 'ua.question_id = q.id')
            ->where([
                'ua.user_id' => $userId,
                'ua.questionnaire_id' => $questionnaireId,
                'q.is_required' => 1,
                'q.status' => 'normal'
            ])->count();

        return $answeredRequiredCount >= $requiredQuestionCount;
    }

    /**
     * 获取用户答题的选项ID列表
     */
    public static function getUserSelectedOptionIds($userId, $questionnaireId)
    {
        $answers = self::where([
            'user_id' => $userId,
            'questionnaire_id' => $questionnaireId
        ])->column('answer_option_ids');

        $optionIds = [];
        foreach ($answers as $answerIds) {
            if ($answerIds) {
                $ids = explode(',', $answerIds);
                $optionIds = array_merge($optionIds, $ids);
            }
        }

        return array_unique($optionIds);
    }

    /**
     * 删除用户的问卷答案
     */
    public static function deleteUserAnswers($userId, $questionnaireId)
    {
        return self::where([
            'user_id' => $userId,
            'questionnaire_id' => $questionnaireId
        ])->delete();
    }

    /**
     * 获取问题的答题统计
     */
    public static function getQuestionAnswerStats($questionId)
    {
        $answers = self::where('question_id', $questionId)->select();
        
        $stats = [];
        foreach ($answers as $answer) {
            $optionIds = explode(',', $answer->answer_option_ids);
            foreach ($optionIds as $optionId) {
                if (!isset($stats[$optionId])) {
                    $stats[$optionId] = 0;
                }
                $stats[$optionId]++;
            }
        }

        return $stats;
    }

    /**
     * 批量保存用户答案
     */
    public static function batchSaveAnswers($userId, $questionnaireId, $answers)
    {
        if (empty($answers) || !is_array($answers)) {
            return false;
        }

        // 先删除用户之前的答案
        self::deleteUserAnswers($userId, $questionnaireId);

        // 批量插入新答案
        $insertData = [];
        foreach ($answers as $questionId => $answerOptionIds) {
            $insertData[] = [
                'user_id' => $userId,
                'questionnaire_id' => $questionnaireId,
                'question_id' => $questionId,
                'answer_option_ids' => is_array($answerOptionIds) ? implode(',', $answerOptionIds) : $answerOptionIds,
                'createtime' => time()
            ];
        }

        if (!empty($insertData)) {
            return \think\Db::name('policy_user_answer')->insertAll($insertData);
        }

        return false;
    }

    /**
     * 验证答案数据
     */
    public static function validateAnswerData($userId, $questionnaireId, $questionId, $answerOptionIds)
    {
        $errors = [];

        // 验证用户ID
        if (empty($userId)) {
            $errors[] = '用户ID不能为空';
        }

        // 验证问卷ID
        if (empty($questionnaireId)) {
            $errors[] = '问卷ID不能为空';
        }

        // 验证问题ID
        if (empty($questionId)) {
            $errors[] = '问题ID不能为空';
        }

        // 验证答案选项
        if (empty($answerOptionIds)) {
            $errors[] = '答案选项不能为空';
        }

        // 验证问题是否属于该问卷
        $question = Question::where([
            'id' => $questionId,
            'questionnaire_id' => $questionnaireId,
            'status' => 'normal'
        ])->find();

        if (!$question) {
            $errors[] = '问题不存在或不属于该问卷';
        }

        return empty($errors) ? true : $errors;
    }
}
